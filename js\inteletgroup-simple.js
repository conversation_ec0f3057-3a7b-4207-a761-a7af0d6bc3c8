// JavaScript simplificado para el formulario de prospectos InteletGroup
// Versión simplificada sin mutex complejo ni múltiples capas de protección

console.log('[SIMPLE] Cargando JavaScript simplificado de InteletGroup');

// Variables globales simples
let isSubmitting = false;
let currentUserName = '';
let currentUserId = 0;

// Inicialización cuando se carga el DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('[SIMPLE] DOM cargado, inicializando sistema simplificado');
    
    // Obtener información del usuario actual
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    }
    if (typeof window.currentUserId !== 'undefined') {
        currentUserId = window.currentUserId;
    }
    
    console.log('[SIMPLE] Usuario actual:', currentUserName, 'ID:', currentUserId);
    
    // Configurar eventos del formulario
    setupFormEvents();
});

// Configurar eventos del formulario
function setupFormEvents() {
    console.log('[SIMPLE] Configurando eventos del formulario');
    
    // Buscar el botón de guardar
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        console.log('[SIMPLE] Botón de guardar encontrado, configurando evento');
        
        // Limpiar eventos anteriores
        saveBtn.replaceWith(saveBtn.cloneNode(true));
        const newSaveBtn = document.getElementById('saveInteletGroupProspectBtn');
        
        // Configurar evento de clic con protección contra múltiples clics
        newSaveBtn.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            // Deshabilitar inmediatamente el botón
            if (this.disabled) {
                console.log('[SIMPLE] Botón ya está deshabilitado, ignorando clic');
                return;
            }

            // Verificar si ya hay un envío en progreso
            if (isSubmitting) {
                console.log('[SIMPLE] Ya hay un envío en progreso, ignorando clic');
                return;
            }

            this.disabled = true;
            console.log('[SIMPLE] Clic en botón guardar detectado');
            handleSaveProspectSimple();
        });
        
        console.log('[SIMPLE] Evento configurado exitosamente');
    } else {
        console.log('[SIMPLE] Botón de guardar no encontrado, reintentando en 1 segundo');
        setTimeout(setupFormEvents, 1000);
    }
    
    // Configurar evento para poblar nombre del ejecutivo cuando se abra el modal
    const modal = document.getElementById('inteletGroupProspectModal');
    if (modal) {
        modal.addEventListener('shown.bs.modal', function() {
            console.log('[SIMPLE] Modal abierto, poblando nombre del ejecutivo');
            populateExecutiveName();
            setupFieldValidations();
            setupUppercaseFields();
        });
    }
    
    // Configurar evento para cambio de tipo de persona
    document.addEventListener('change', function(event) {
        if (event.target && event.target.id === 'tipo_persona') {
            console.log('[SIMPLE] Tipo de persona cambiado a:', event.target.value);
            loadDocumentChecklist(event.target.value);
        }
    });
}

// Poblar nombre del ejecutivo
function populateExecutiveName() {
    const nameField = document.getElementById('nombre_ejecutivo');
    if (nameField && currentUserName) {
        nameField.value = currentUserName;
        console.log('[SIMPLE] Nombre del ejecutivo poblado:', currentUserName);
    }
}

// Función principal simplificada para guardar prospecto
async function handleSaveProspectSimple() {
    console.log('[SIMPLE] ====== INICIO GUARDADO SIMPLIFICADO ======');
    
    // Verificación simple de doble envío
    if (isSubmitting) {
        console.log('[SIMPLE] Ya hay un envío en progreso, ignorando');
        return;
    }
    
    // Marcar como enviando
    isSubmitting = true;
    
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    
    try {
        // Deshabilitar botón
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        }
        
        // Validar formulario
        const form = document.getElementById('inteletGroupProspectForm');
        if (!form) {
            throw new Error('Formulario no encontrado');
        }
        
        if (!validateFormSimple(form)) {
            throw new Error('Por favor complete todos los campos requeridos');
        }
        
        // Recopilar datos del formulario
        const formData = new FormData(form);
        
        // Agregar usuario_id si está disponible
        if (currentUserId) {
            formData.append('usuario_id', currentUserId);
        }
        
        // Agregar archivos cargados
        for (const [docId, files] of Object.entries(uploadedFiles)) {
            files.forEach((file, index) => {
                formData.append(`doc_${docId}_${index}`, file);
            });
        }
        
        console.log('[SIMPLE] Enviando datos al servidor...');
        
        // Enviar datos al servidor
        const response = await fetch('guardar_prospecto_inteletgroup.php', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status}`);
        }
        
        // Primero obtener el texto de la respuesta
        const responseText = await response.text();
        console.log('[SIMPLE] Status HTTP:', response.status);
        console.log('[SIMPLE] Status Text:', response.statusText);
        console.log('[SIMPLE] Headers:', response.headers);
        console.log('[SIMPLE] Longitud respuesta:', responseText.length);
        console.log('[SIMPLE] Respuesta cruda del servidor:', responseText);
        
        // Si la respuesta está vacía
        if (!responseText || responseText.trim() === '') {
            console.error('[SIMPLE] RESPUESTA VACÍA DEL SERVIDOR');
            throw new Error('El servidor no devolvió ninguna respuesta. Posible error fatal de PHP.');
        }
        
        // Intentar parsear como JSON
        let result;
        try {
            result = JSON.parse(responseText);
        } catch (parseError) {
            console.error('[SIMPLE] Error parseando JSON:', parseError);
            console.error('[SIMPLE] Primeros 500 caracteres:', responseText.substring(0, 500));
            console.error('[SIMPLE] Últimos 500 caracteres:', responseText.substring(responseText.length - 500));
            
            // Si contiene "br" o "Warning", es un error PHP
            if (responseText.includes('<br') || responseText.includes('Warning') || responseText.includes('Fatal error') || responseText.includes('Parse error')) {
                console.error('[SIMPLE] DETECTADO ERROR PHP EN LA RESPUESTA');
                throw new Error('Error del servidor PHP. Revise los logs del servidor para más detalles.');
            } else {
                throw new Error('Respuesta inválida del servidor: ' + responseText.substring(0, 100));
            }
        }
        
        console.log('[SIMPLE] Respuesta del servidor:', result);
        
        if (result.success) {
            console.log('[SIMPLE] Prospecto guardado exitosamente');
            showMessage('success', result.message || 'Prospecto guardado exitosamente');

            // IMPORTANTE: Resetear flag de envío en caso de éxito
            isSubmitting = false;

            // Cerrar modal después de un breve delay
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('inteletGroupProspectModal'));
                if (modal) {
                    modal.hide();
                }

                // Recargar la página o actualizar la tabla
                if (typeof location !== 'undefined') {
                    location.reload();
                }
            }, 1500);

        } else {
            throw new Error(result.message || 'Error desconocido al guardar el prospecto');
        }
        
    } catch (error) {
        console.error('[SIMPLE] Error al guardar prospecto:', error);
        showMessage('error', error.message || 'Error al guardar el prospecto');
        
        // Solo rehabilitar el botón en caso de error
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Guardar Prospecto';
        }
        
        // Resetear flag
        isSubmitting = false;
        
    } finally {
        console.log('[SIMPLE] ====== FIN GUARDADO SIMPLIFICADO ======');
    }
}

// Validación simple del formulario
function validateFormSimple(form) {
    console.log('[SIMPLE] Validando formulario...');
    
    const requiredFields = [
        'rut_cliente',
        'razon_social', 
        'rubro',
        'direccion_comercial',
        'telefono_celular',
        'email',
        'tipo_persona',
        'tipo_cuenta',
        'numero_cuenta_bancaria',
        'dias_atencion',
        'horario_atencion',
        'contrata_boleta',
        'competencia_actual'
    ];
    
    for (const fieldName of requiredFields) {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (!field || !field.value.trim()) {
            console.log('[SIMPLE] Campo requerido faltante:', fieldName);
            showMessage('error', `El campo ${fieldName.replace('_', ' ')} es requerido`);
            return false;
        }
    }
    
    console.log('[SIMPLE] Validación exitosa');
    return true;
}

// Función simple para mostrar mensajes
function showMessage(type, message) {
    console.log(`[SIMPLE] Mensaje ${type}:`, message);
    
    // Intentar usar SweetAlert si está disponible
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: type === 'success' ? 'success' : 'error',
            title: type === 'success' ? 'Éxito' : 'Error',
            text: message,
            timer: type === 'success' ? 2000 : 5000,
            showConfirmButton: type !== 'success'
        });
    } else {
        // Fallback a alert nativo
        alert(message);
    }
}

// Función global para abrir el modal (mantener compatibilidad)
window.abrirModalInteletGroupProspecto = function() {
    console.log('[SIMPLE] Abriendo modal de prospecto');
    
    const modal = new bootstrap.Modal(document.getElementById('inteletGroupProspectModal'));
    modal.show();
    
    // Resetear formulario
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.reset();
    }
    
    // Poblar nombre del ejecutivo
    setTimeout(populateExecutiveName, 100);
};

// Configurar campos en mayúsculas
function setupUppercaseFields() {
    console.log('[SIMPLE] Configurando campos en mayúsculas');
    
    const uppercaseFields = ['razon_social', 'rubro', 'direccion_comercial', 'dias_atencion'];
    
    uppercaseFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            // Transformar a mayúsculas mientras se escribe
            field.addEventListener('input', function() {
                const cursorPos = this.selectionStart;
                this.value = this.value.toUpperCase();
                this.setSelectionRange(cursorPos, cursorPos);
            });
            
            // También en blur por si acaso
            field.addEventListener('blur', function() {
                this.value = this.value.toUpperCase();
            });
            
            console.log('[SIMPLE] Campo configurado para mayúsculas:', fieldId);
        }
    });
}

// Configurar validaciones de campos
function setupFieldValidations() {
    console.log('[SIMPLE] Configurando validaciones de campos');
    
    // Validación de RUT
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.addEventListener('blur', function() {
            formatRUT(this);
            validateRUT(this);
        });
        rutField.addEventListener('input', function() {
            clearFieldError(this);
        });
    }
    
    // Validación de teléfono
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField) {
        telefonoField.addEventListener('input', function() {
            // Solo permitir números
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    }
    
    // Validación de email
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
}

// Formatear RUT
function formatRUT(field) {
    let value = field.value.replace(/[^0-9kK]/g, '');
    if (value.length > 1) {
        value = value.slice(0, -1) + '-' + value.slice(-1);
    }
    field.value = value.toUpperCase();
}

// Validar RUT
function validateRUT(field) {
    const rut = field.value.replace(/[^0-9kK]/g, '');
    if (rut.length < 7) {
        showFieldError(field, 'RUT debe tener al menos 7 caracteres');
        return false;
    }
    
    // Validación del dígito verificador
    const body = rut.slice(0, -1);
    const dv = rut.slice(-1).toUpperCase();
    
    let sum = 0;
    let multiplier = 2;
    
    for (let i = body.length - 1; i >= 0; i--) {
        sum += parseInt(body.charAt(i)) * multiplier;
        multiplier = multiplier < 7 ? multiplier + 1 : 2;
    }
    
    const expectedDV = 11 - (sum % 11);
    const calculatedDV = expectedDV === 11 ? '0' : expectedDV === 10 ? 'K' : expectedDV.toString();
    
    if (dv !== calculatedDV) {
        showFieldError(field, 'RUT inválido');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

// Validar email
function validateEmail(field) {
    const email = field.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(email)) {
        showFieldError(field, 'Email inválido');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

// Mostrar error en campo
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    const feedback = field.parentElement.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.textContent = message;
    }
}

// Limpiar error de campo
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const feedback = field.parentElement.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.textContent = '';
    }
}

// Variables para gestión de archivos
let uploadedFiles = {};

// Cargar checklist de documentos según tipo de persona
function loadDocumentChecklist(tipoPersona) {
    console.log('[SIMPLE] Cargando checklist de documentos para:', tipoPersona);
    
    const container = document.getElementById('document-checklist-container');
    if (!container) {
        console.log('[SIMPLE] Contenedor de checklist no encontrado');
        return;
    }
    
    // Limpiar contenido anterior y archivos
    container.innerHTML = '';
    uploadedFiles = {};
    
    if (!tipoPersona) {
        container.innerHTML = '<div class="alert alert-info">Seleccione un tipo de persona para ver los documentos requeridos.</div>';
        return;
    }
    
    // Mostrar loading
    container.innerHTML = '<div class="text-center p-3"><i class="bi bi-hourglass-split me-2"></i>Cargando documentos...</div>';
    
    // Cargar documentos desde la base de datos
    fetch('endpoints/obtener_tipos_documento_v2.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tipo_persona: tipoPersona })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.documentos && data.documentos.length > 0) {
            console.log('[SIMPLE] Documentos cargados desde BD:', data.documentos);
            renderDocumentChecklistFromDB(data.documentos);
        } else {
            console.log('[SIMPLE] Usando documentos por defecto');
            // Fallback a documentos por defecto
            renderDocumentChecklistDefault(tipoPersona);
        }
    })
    .catch(error => {
        console.error('[SIMPLE] Error cargando documentos:', error);
        // Fallback a documentos por defecto
        renderDocumentChecklistDefault(tipoPersona);
    });
}

// Renderizar checklist desde base de datos
function renderDocumentChecklistFromDB(documentos) {
    const container = document.getElementById('document-checklist-container');
    if (!container) return;
    
    // Convertir documentos de BD a formato esperado
    const documents = documentos.map(doc => ({
        id: doc.codigo,
        name: doc.nombre,
        description: doc.descripcion || '',
        required: doc.es_obligatorio === 1
    }));
    
    renderDocumentChecklist(documents);
}

// Renderizar checklist por defecto
function renderDocumentChecklistDefault(tipoPersona) {
    const documentosDefault = {
        'Natural': [
            { id: 'cedula_identidad', name: 'Cédula de Identidad', description: 'Documento de identificación personal', required: true },
            { id: 'comprobante_domicilio', name: 'Comprobante de Domicilio', description: 'Boleta de servicios o similar', required: true },
            { id: 'carpeta_tributaria', name: 'Carpeta Tributaria', description: 'Documentación tributaria del SII', required: false }
        ],
        'Juridica': [
            { id: 'escritura_constitucion', name: 'Escritura de Constitución', description: 'Escritura de constitución de la empresa', required: true },
            { id: 'poder_representante', name: 'Poder del Representante Legal', description: 'Poder notarial del representante', required: true },
            { id: 'cedula_representante', name: 'Cédula Representante Legal', description: 'Cédula de identidad del representante', required: true },
            { id: 'carpeta_tributaria', name: 'Carpeta Tributaria', description: 'Documentación tributaria del SII', required: true },
            { id: 'certificado_vigencia', name: 'Certificado de Vigencia', description: 'Certificado de vigencia de la sociedad', required: false }
        ]
    };
    
    const documents = documentosDefault[tipoPersona] || [];
    renderDocumentChecklist(documents);
}

// Renderizar checklist común
function renderDocumentChecklist(documents) {
    const container = document.getElementById('document-checklist-container');
    if (!container) return;
    
    let html = '<div class="document-checklist">';
    
    // Barra de progreso
    const requiredCount = documents.filter(d => d.required).length;
    html += `
        <div class="checklist-progress mb-4">
            <div class="d-flex justify-content-between mb-2">
                <span class="fw-semibold">Progreso de documentación</span>
                <span id="checklist-progress-text" class="text-muted">0 de ${requiredCount} obligatorios</span>
            </div>
            <div class="progress" style="height: 8px;">
                <div class="progress-bar bg-success" id="checklist-progress-bar"
                     role="progressbar" style="width: 0%"></div>
            </div>
        </div>
    `;
    
    // Separar documentos obligatorios y opcionales
    const requiredDocs = documents.filter(d => d.required);
    const optionalDocs = documents.filter(d => !d.required);
    
    if (requiredDocs.length > 0) {
        html += '<h6 class="mb-3"><i class="bi bi-exclamation-circle text-danger me-2"></i>Documentos Obligatorios</h6>';
        requiredDocs.forEach(doc => {
            html += createDocumentChecklistItem(doc);
        });
    }
    
    if (optionalDocs.length > 0) {
        html += '<h6 class="mb-3 mt-4"><i class="bi bi-info-circle text-info me-2"></i>Documentos Opcionales</h6>';
        optionalDocs.forEach(doc => {
            html += createDocumentChecklistItem(doc);
        });
    }
    
    html += '</div>';
    container.innerHTML = html;
    
    // Configurar eventos
    setupChecklistEvents();
    updateProgressBar();
}

// Crear item del checklist
function createDocumentChecklistItem(doc) {
    const requiredBadge = doc.required ? 
        '<span class="badge bg-danger ms-2">Obligatorio</span>' : 
        '<span class="badge bg-secondary ms-2">Opcional</span>';
    
    return `
        <div class="checklist-item card mb-3 border" data-doc-id="${doc.id}" data-required="${doc.required}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h6 class="mb-1">
                            <i class="bi bi-file-earmark me-2"></i>
                            ${doc.name}
                            ${requiredBadge}
                        </h6>
                        ${doc.description ? `<small class="text-muted">${doc.description}</small>` : ''}
                    </div>
                    <div class="checklist-status text-warning" id="status-${doc.id}">
                        <i class="bi bi-clock-fill"></i>
                        <small>Pendiente</small>
                    </div>
                </div>
                
                <div class="upload-area border border-2 border-dashed rounded p-3 text-center bg-light" 
                     onclick="triggerFileUpload('${doc.id}')"
                     ondragover="handleDragOver(event)"
                     ondragleave="handleDragLeave(event)"
                     ondrop="handleDrop(event, '${doc.id}')">
                    <i class="bi bi-cloud-upload fs-3 text-muted"></i>
                    <p class="mb-0 mt-2">Clic para seleccionar o arrastre archivos aquí</p>
                    <small class="text-muted">PDF, JPG, PNG, DOC, DOCX - Máx: 5MB</small>
                </div>
                
                <input type="file" 
                       class="d-none" 
                       id="file-${doc.id}" 
                       name="documentos[${doc.id}][]"
                       accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                       multiple
                       onchange="handleFileSelection(this, '${doc.id}')">
                
                <div class="uploaded-files-list mt-2" id="files-list-${doc.id}"></div>
            </div>
        </div>
    `;
}

// Configurar eventos del checklist
function setupChecklistEvents() {
    // Prevenir comportamiento por defecto del drag & drop
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, preventDefaults, false);
    });
}

// Prevenir comportamientos por defecto
function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

// Trigger para abrir selector de archivos
window.triggerFileUpload = function(docId) {
    document.getElementById(`file-${docId}`).click();
};

// Manejar drag over
window.handleDragOver = function(e) {
    preventDefaults(e);
    e.currentTarget.classList.add('bg-primary', 'bg-opacity-10');
};

// Manejar drag leave
window.handleDragLeave = function(e) {
    preventDefaults(e);
    e.currentTarget.classList.remove('bg-primary', 'bg-opacity-10');
};

// Manejar drop
window.handleDrop = function(e, docId) {
    preventDefaults(e);
    e.currentTarget.classList.remove('bg-primary', 'bg-opacity-10');
    
    const files = e.dataTransfer.files;
    const input = document.getElementById(`file-${docId}`);
    input.files = files;
    handleFileSelection(input, docId);
};

// Manejar selección de archivos
window.handleFileSelection = function(input, docId) {
    const files = Array.from(input.files);
    
    if (files.length === 0) return;
    
    // Inicializar array si no existe
    if (!uploadedFiles[docId]) {
        uploadedFiles[docId] = [];
    }
    
    // Validar y agregar archivos
    files.forEach(file => {
        if (file.size > 5 * 1024 * 1024) {
            showMessage('error', `El archivo ${file.name} excede el tamaño máximo de 5MB`);
            return;
        }
        
        uploadedFiles[docId].push(file);
    });
    
    // Actualizar UI
    updateFilesList(docId);
    updateDocumentStatus(docId);
    updateProgressBar();
};

// Actualizar lista de archivos
function updateFilesList(docId) {
    const container = document.getElementById(`files-list-${docId}`);
    if (!container) return;
    
    const files = uploadedFiles[docId] || [];
    
    if (files.length === 0) {
        container.innerHTML = '';
        return;
    }
    
    let html = '<div class="mt-2">';
    files.forEach((file, index) => {
        html += `
            <div class="d-flex justify-content-between align-items-center bg-light rounded p-2 mb-1">
                <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-check text-success me-2"></i>
                    <small>${file.name}</small>
                    <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                </div>
                <button type="button" class="btn btn-sm btn-link text-danger p-0" 
                        onclick="removeFile('${docId}', ${index})">
                    <i class="bi bi-x-circle"></i>
                </button>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

// Actualizar estado del documento
function updateDocumentStatus(docId) {
    const statusEl = document.getElementById(`status-${docId}`);
    const files = uploadedFiles[docId] || [];
    
    if (files.length > 0) {
        statusEl.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i> <small>Completado</small>';
        statusEl.className = 'checklist-status text-success';
    } else {
        statusEl.innerHTML = '<i class="bi bi-clock-fill"></i> <small>Pendiente</small>';
        statusEl.className = 'checklist-status text-warning';
    }
}

// Actualizar barra de progreso
function updateProgressBar() {
    const requiredItems = document.querySelectorAll('.checklist-item[data-required="true"]');
    const totalRequired = requiredItems.length;
    let completedRequired = 0;
    
    requiredItems.forEach(item => {
        const docId = item.dataset.docId;
        if (uploadedFiles[docId] && uploadedFiles[docId].length > 0) {
            completedRequired++;
        }
    });
    
    const percentage = totalRequired > 0 ? (completedRequired / totalRequired) * 100 : 0;
    
    const progressBar = document.getElementById('checklist-progress-bar');
    const progressText = document.getElementById('checklist-progress-text');
    
    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }
    
    if (progressText) {
        progressText.textContent = `${completedRequired} de ${totalRequired} obligatorios`;
    }
}

// Remover archivo
window.removeFile = function(docId, index) {
    if (uploadedFiles[docId]) {
        uploadedFiles[docId].splice(index, 1);
        updateFilesList(docId);
        updateDocumentStatus(docId);
        updateProgressBar();
    }
};

// Formatear tamaño de archivo
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Generar RUT aleatorio para pruebas
window.generarRutAleatorio = function() {
    const min = 10000000;
    const max = 25000000;
    const num = Math.floor(Math.random() * (max - min + 1)) + min;
    
    // Calcular dígito verificador
    let sum = 0;
    let multiplier = 2;
    const numStr = num.toString();
    
    for (let i = numStr.length - 1; i >= 0; i--) {
        sum += parseInt(numStr.charAt(i)) * multiplier;
        multiplier = multiplier < 7 ? multiplier + 1 : 2;
    }
    
    const dv = 11 - (sum % 11);
    const dvFinal = dv === 11 ? '0' : dv === 10 ? 'K' : dv.toString();
    
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.value = numStr + '-' + dvFinal;
        validateRUT(rutField);
    }
};

console.log('[SIMPLE] JavaScript simplificado cargado completamente');
