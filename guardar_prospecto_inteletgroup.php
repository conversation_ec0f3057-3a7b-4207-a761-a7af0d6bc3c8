<?php
// Configuración de errores - desactivar display para producción
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Función personalizada para manejar errores fatales
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && ($error['type'] === E_ERROR || $error['type'] === E_PARSE || $error['type'] === E_COMPILE_ERROR)) {
        ob_clean();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error fatal del servidor: ' . $error['message'],
            'debug' => [
                'file' => $error['file'],
                'line' => $error['line']
            ]
        ]);
        exit;
    }
});

// Iniciar buffer de salida para capturar cualquier error
ob_start();

// Establecer cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar método de solicitud
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Incluir archivo de conexión
try {
    require_once 'con_db.php';
} catch (Exception $e) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Error al incluir archivo de conexión: ' . $e->getMessage()
    ]);
    exit;
}

// Verificar si la conexión fue exitosa
if (!isset($conexion) || $conexion->connect_error) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Error de conexión a la base de datos'
    ]);
    exit;
}

// Obtener los datos del formulario
$usuario_id = $_POST['usuario_id'] ?? null;
$nombre_ejecutivo = $_POST['nombre_ejecutivo'] ?? '';
$rut_cliente = $_POST['rut_cliente'] ?? '';
$razon_social = $_POST['razon_social'] ?? '';
$rubro = $_POST['rubro'] ?? '';
$direccion_comercial = $_POST['direccion_comercial'] ?? '';
$telefono_celular = $_POST['telefono_celular'] ?? '';
$email = $_POST['email'] ?? '';
$tipo_persona = $_POST['tipo_persona'] ?? '';
$numero_pos = $_POST['numero_pos'] ?? '';
$tipo_cuenta = $_POST['tipo_cuenta'] ?? '';
$numero_cuenta_bancaria = $_POST['numero_cuenta_bancaria'] ?? '';
$dias_atencion = $_POST['dias_atencion'] ?? '';
$horario_atencion = $_POST['horario_atencion'] ?? '';
$contrata_boleta = $_POST['contrata_boleta'] ?? '';
$competencia_actual = $_POST['competencia_actual'] ?? '';

// Validar campos requeridos
if (empty($rut_cliente) || empty($razon_social) || empty($rubro) ||
    empty($direccion_comercial) || empty($telefono_celular) || empty($email) ||
    empty($tipo_persona) || empty($tipo_cuenta) || empty($numero_cuenta_bancaria) ||
    empty($dias_atencion) || empty($horario_atencion) || empty($contrata_boleta) ||
    empty($competencia_actual)) {

    echo json_encode([
        'success' => false,
        'message' => 'Todos los campos requeridos deben ser completados'
    ]);
    exit;
}

// Validar que usuario_id esté presente
if (empty($usuario_id)) {
    echo json_encode([
        'success' => false,
        'message' => 'ID de usuario requerido'
    ]);
    exit;
}

try {
    // Verificar si ya existe un prospecto con el mismo RUT creado en los últimos 5 minutos
    $checkSql = "SELECT id, fecha_registro FROM tb_inteletgroup_prospectos 
                 WHERE rut_cliente = ? 
                 AND fecha_registro >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
                 ORDER BY fecha_registro DESC LIMIT 1";
    
    $checkStmt = $conexion->prepare($checkSql);
    $checkStmt->bind_param("s", $rut_cliente);
    $checkStmt->execute();
    
    // Usar store_result() en lugar de get_result()
    $checkStmt->store_result();
    
    if ($checkStmt->num_rows > 0) {
        // Obtener el ID usando bind_result
        $existing_id = null;
        $checkStmt->bind_result($existing_id);
        $checkStmt->fetch();
        
        echo json_encode([
            'success' => false,
            'message' => 'Ya existe un prospecto con este RUT registrado recientemente. Por favor, verifique la información.',
            'duplicate' => true,
            'existing_id' => $existing_id
        ]);
        exit;
    }
    $checkStmt->close();
    
    // Verificar si ya existe un prospecto con el mismo RUT
    $check_duplicate_sql = "SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?";
    $check_stmt = $conexion->prepare($check_duplicate_sql);

    if (!$check_stmt) {
        echo json_encode([
            'success' => false,
            'message' => 'Error al preparar consulta de verificación: ' . $conexion->error
        ]);
        exit;
    }

    $check_stmt->bind_param("s", $rut_cliente);
    $check_stmt->execute();

    // Usar store_result() en lugar de get_result() para compatibilidad con PHP 7.3.33
    $check_stmt->store_result();

    if ($check_stmt->num_rows > 0) {
        $check_stmt->close();
        echo json_encode([
            'success' => false,
            'message' => 'Ya existe un prospecto registrado con el RUT: ' . $rut_cliente
        ]);
        exit;
    }
    $check_stmt->close();

    // Preparar la consulta SQL para insertar en la tabla principal tb_inteletgroup_prospectos
    $sql = "INSERT INTO tb_inteletgroup_prospectos (
        usuario_id,
        nombre_ejecutivo,
        rut_cliente,
        razon_social,
        rubro,
        direccion_comercial,
        telefono_celular,
        email,
        tipo_persona,
        numero_pos,
        tipo_cuenta,
        numero_cuenta_bancaria,
        dias_atencion,
        horario_atencion,
        contrata_boleta,
        competencia_actual
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conexion->prepare($sql);

    if (!$stmt) {
        throw new Exception("Error al preparar la consulta: " . $conexion->error);
    }

    $stmt->bind_param("isssssssssssssss",
        $usuario_id,
        $nombre_ejecutivo,
        $rut_cliente,
        $razon_social,
        $rubro,
        $direccion_comercial,
        $telefono_celular,
        $email,
        $tipo_persona,
        $numero_pos,
        $tipo_cuenta,
        $numero_cuenta_bancaria,
        $dias_atencion,
        $horario_atencion,
        $contrata_boleta,
        $competencia_actual
    );
    
    // Ejecutar la consulta
    if ($stmt->execute()) {
        $prospecto_id = $conexion->insert_id;
        
        // Manejo de archivos
        $documentos_procesados = [];
        $upload_errors = [];
        
        // Crear directorio para archivos si no existe
        $upload_dir = 'uploads/inteletgroup/' . date('Y/m/');
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        // Procesar archivos adjuntos
        if (!empty($_FILES)) {
            error_log("DEBUG: Estructura de \$_FILES: " . print_r($_FILES, true));
            
            foreach ($_FILES as $field_name => $file) {
                error_log("DEBUG: Procesando campo '$field_name' con estructura: " . print_r($file, true));
                
                // Saltar si no hay archivos reales
                if (empty($file['name']) || $file['error'] === UPLOAD_ERR_NO_FILE) continue;
                
                // Verificar si es un archivo individual o múltiple
                if (is_array($file['name'])) {
                    // Manejar archivos múltiples
                    for ($i = 0; $i < count($file['name']); $i++) {
                        // Saltar si no hay archivo en este índice
                        if (empty($file['name'][$i]) || $file['error'][$i] === UPLOAD_ERR_NO_FILE) continue;
                        
                        if ($file['error'][$i] === UPLOAD_ERR_OK) {
                            $tmp_name = $file['tmp_name'][$i];
                            $original_name = $file['name'][$i];
                            $file_size = $file['size'][$i];
                            
                            // Validar tamaño (5MB máximo)
                            if ($file_size > 5 * 1024 * 1024) {
                                $upload_errors[] = "Archivo $original_name excede el tamaño máximo de 5MB";
                                continue;
                            }
                            
                            // Generar nombre único
                            $file_ext = pathinfo($original_name, PATHINFO_EXTENSION);
                            $new_filename = $rut_cliente . '_' . $field_name . '_' . uniqid() . '.' . $file_ext;
                            $destination = $upload_dir . $new_filename;
                            
                            // Mover archivo
                            if (move_uploaded_file($tmp_name, $destination)) {
                                // Determinar tipo MIME
                                $extension_to_mime = [
                                    'pdf' => 'application/pdf',
                                    'doc' => 'application/msword',
                                    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                    'jpg' => 'image/jpeg',
                                    'jpeg' => 'image/jpeg',
                                    'png' => 'image/png',
                                    'xls' => 'application/vnd.ms-excel',
                                    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                    'txt' => 'text/plain',
                                    'csv' => 'text/csv',
                                    'rtf' => 'application/rtf',
                                    'zip' => 'application/zip'
                                ];
                                
                                $ext_lower = strtolower($file_ext);
                                $file_type = isset($extension_to_mime[$ext_lower]) ? $extension_to_mime[$ext_lower] : $ext_lower;
                                $doc_sql = "INSERT INTO tb_inteletgroup_documentos (
                                    prospecto_id, tipo_documento_id, nombre_archivo, nombre_original,
                                    tipo_archivo, tamaño_archivo, ruta_archivo, usuario_id, rut_cliente
                                ) VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?)"; // tipo_documento_id = 1 temporal
                                
                                $doc_stmt = $conexion->prepare($doc_sql);
                                $doc_stmt->bind_param("isssisis", 
                                    $prospecto_id, 
                                    $new_filename,
                                    $original_name,
                                    $file_type,
                                    $file_size, 
                                    $destination, 
                                    $usuario_id,
                                    $rut_cliente
                                );
                                $doc_stmt->execute();
                                $doc_stmt->close();
                                
                                $documentos_procesados[] = [
                                    'campo' => $field_name,
                                    'archivo' => $original_name,
                                    'ruta' => $destination
                                ];
                            } else {
                                $upload_errors[] = "Error al subir archivo $original_name";
                            }
                        }
                    }
                } else {
                    // Archivo único
                    if ($file['error'] === UPLOAD_ERR_OK) {
                        $tmp_name = $file['tmp_name'];
                        $original_name = $file['name'];
                        $file_size = $file['size'];
                        
                        // Validar tamaño (5MB máximo)
                        if ($file_size > 5 * 1024 * 1024) {
                            $upload_errors[] = "Archivo $original_name excede el tamaño máximo de 5MB";
                            continue;
                        }
                        
                        // Generar nombre único
                        $file_ext = pathinfo($original_name, PATHINFO_EXTENSION);
                        $new_filename = $rut_cliente . '_' . $field_name . '_' . uniqid() . '.' . $file_ext;
                        $destination = $upload_dir . $new_filename;
                        
                        // Mover archivo
                        if (move_uploaded_file($tmp_name, $destination)) {
                            // Determinar tipo MIME
                            $extension_to_mime = [
                                'pdf' => 'application/pdf',
                                'doc' => 'application/msword',
                                'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'jpg' => 'image/jpeg',
                                'jpeg' => 'image/jpeg',
                                'png' => 'image/png',
                                'xls' => 'application/vnd.ms-excel',
                                'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'txt' => 'text/plain',
                                'csv' => 'text/csv',
                                'rtf' => 'application/rtf',
                                'zip' => 'application/zip'
                            ];
                            
                            $ext_lower = strtolower($file_ext);
                            $file_type = isset($extension_to_mime[$ext_lower]) ? $extension_to_mime[$ext_lower] : $ext_lower;
                            $doc_sql = "INSERT INTO tb_inteletgroup_documentos (
                                prospecto_id, tipo_documento_id, nombre_archivo, nombre_original,
                                tipo_archivo, tamaño_archivo, ruta_archivo, usuario_id, rut_cliente
                            ) VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?)"; // tipo_documento_id = 1 temporal
                            
                            $doc_stmt = $conexion->prepare($doc_sql);
                            $doc_stmt->bind_param("isssisis", 
                                $prospecto_id, 
                                $new_filename,
                                $original_name,
                                $file_type,
                                $file_size, 
                                $destination, 
                                $usuario_id,
                                $rut_cliente
                            );
                            $doc_stmt->execute();
                            $doc_stmt->close();
                            
                            $documentos_procesados[] = [
                                'campo' => $field_name,
                                'archivo' => $original_name,
                                'ruta' => $destination
                            ];
                        } else {
                            $upload_errors[] = "Error al subir archivo $original_name";
                        }
                    }
                }
            }
        }
        
        // Preparar respuesta
        $response = [
            'success' => true,
            'message' => 'Prospecto guardado exitosamente',
            'prospecto_id' => $prospecto_id,
            'documentos' => $documentos_procesados
        ];
        
        // Agregar errores de carga si existen
        if (!empty($upload_errors)) {
            $response['upload_errors'] = $upload_errors;
            $response['message'] .= '. Algunos archivos no pudieron ser cargados.';
        }
        
        // Limpiar cualquier salida anterior y enviar JSON
        $buffer = ob_get_clean();
        if (!empty($buffer)) {
            error_log("ADVERTENCIA: Salida no esperada capturada: " . $buffer);
        }
        
        echo json_encode($response);
    } else {
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    // Limpiar cualquier salida anterior y enviar error
    $buffer = ob_get_clean();
    if (!empty($buffer)) {
        error_log("ADVERTENCIA: Salida no esperada capturada en error: " . $buffer);
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Error al guardar el prospecto: ' . $e->getMessage()
    ]);
}

// Cerrar la conexión
if (isset($conexion)) {
    $conexion->close();
}